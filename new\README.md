# 🍍 جزيرة الأناناس - مغامرة تعلم اللغة الإنجليزية

لعبة تعليمية تفاعلية شاملة لتعلم اللغة الإنجليزية للأطفال من الصف الأول إلى السادس الابتدائي.

## 📁 هيكل المشروع

```
new/
├── index.html              # الصفحة الرئيسية
├── styles/
│   └── main.css           # ملف الأنماط الرئيسي
├── js/
│   ├── main.js            # الوظائف الرئيسية للعبة
│   ├── games.js           # وظائف الألعاب العامة
│   ├── level1.js          # ألعاب المستوى الأول
│   ├── level2.js          # ألعاب المستوى الثاني (قيد التطوير)
│   ├── level3.js          # ألعاب المستوى الثالث (قيد التطوير)
│   ├── level4.js          # ألعاب المستوى الرابع (قيد التطوير)
│   ├── level5.js          # ألعاب المستوى الخامس (قيد التطوير)
│   └── level6.js          # ألعاب المستوى السادس (قيد التطوير)
├── images/                # مجلد الصور
│   ├── placeholder.jpg    # صورة بديلة
│   ├── apple.jpg         # صور المفردات
│   ├── ball.jpg
│   └── ...               # باقي الصور
├── sounds/               # مجلد الأصوات (اختياري)
│   ├── correct.mp3       # صوت الإجابة الصحيحة
│   ├── wrong.mp3         # صوت الإجابة الخاطئة
│   └── ...               # باقي الأصوات
└── README.md             # هذا الملف
```

## 🎮 المستويات والألعاب

### المستوى الأول - الصف الأول الابتدائي ✅
- **لعبة الحروف الأبجدية**: تعلم الحروف من A إلى Z
- **لعبة الألوان**: تعلم الألوان الأساسية
- **لعبة الأرقام**: تعلم الأرقام من 1 إلى 10
- **لعبة العائلة**: تعلم أفراد العائلة
- **لعبة أجزاء الجسم**: تعلم أجزاء الجسم الأساسية
- **لعبة الحيوانات**: تعلم أسماء الحيوانات
- **لعبة الطعام**: تعلم أسماء الطعام والشراب

### المستوى الثاني - الصف الثاني الابتدائي 🚧
- لعبة المدرسة
- لعبة أيام الأسبوع
- لعبة الملابس
- لعبة حيوانات المزرعة
- لعبة الطبيعة

### المستوى الثالث - الصف الثالث الابتدائي 🚧
- لعبة المشاعر
- لعبة الصحة
- لعبة المدينة
- لعبة الوظائف
- لعبة البيئة

### المستوى الرابع - الصف الرابع الابتدائي 🚧
- لعبة الطعام المتقدم
- لعبة الحيوانات وبيئاتها
- لعبة النباتات
- لعبة المباني
- لعبة الأجهزة

### المستوى الخامس - الصف الخامس الابتدائي 🚧
- لعبة الزراعة
- لعبة الرياضة
- لعبة المناخ
- لعبة السياحة
- لعبة الحياة البرية

### المستوى السادس - الصف السادس الابتدائي 🚧
- لعبة المجتمع
- لعبة تغير المناخ
- لعبة الحضارات القديمة
- لعبة التكنولوجيا
- لعبة القصص

## 🎯 الميزات

### ✅ المطورة حالياً
- واجهة مستخدم متجاوبة تدعم جميع الشاشات
- نظام تنقل سهل بين المستويات
- ألعاب تفاعلية للمستوى الأول
- نظام نطق للكلمات (عربي وإنجليزي)
- نظام تقييم وعرض النتائج
- رسائل تشجيعية مخصصة
- حفظ التقدم محلياً
- تأثيرات بصرية جذابة

### 🚧 قيد التطوير
- باقي مستويات اللعبة (2-6)
- نظام الأصوات المحسن
- المزيد من أنواع الألعاب
- نظام الإنجازات
- تقارير تقدم مفصلة

## 🛠️ التقنيات المستخدمة

- **HTML5**: هيكل الصفحة
- **CSS3**: التصميم والتأثيرات البصرية
- **JavaScript**: المنطق والتفاعل
- **Web Speech API**: النطق
- **Local Storage**: حفظ التقدم
- **Canvas API**: إنشاء الصور البديلة

## 📱 التجاوب

اللعبة مصممة لتعمل بشكل مثالي على:
- 💻 أجهزة الحاسوب
- 📱 الهواتف الذكية
- 📱 الأجهزة اللوحية
- 🖥️ الشاشات الكبيرة

## 🎨 التصميم

- ألوان زاهية وجذابة للأطفال
- خطوط واضحة وسهلة القراءة
- تأثيرات بصرية ممتعة
- واجهة بديهية وسهلة الاستخدام

## 🔊 الصوت

- نطق واضح للكلمات العربية والإنجليزية
- مؤثرات صوتية للتفاعل
- إمكانية تشغيل/إيقاف الصوت

## 📊 نظام التقييم

- نقاط لكل إجابة صحيحة
- نسبة الإجابات الصحيحة
- رسائل تشجيعية مخصصة
- نجوم بناءً على الأداء
- إمكانية إعادة اللعب

## 🚀 كيفية التشغيل

1. افتح ملف `index.html` في المتصفح
2. اختر المستوى المناسب
3. ابدأ اللعب والتعلم!

## 📝 ملاحظات للتطوير

### الصور المطلوبة
يجب إضافة الصور التالية في مجلد `images/`:
- apple.jpg, ball.jpg, cat.jpg, dog.jpg, elephant.jpg
- fish.jpg, giraffe.jpg, house.jpg
- father.jpg, mother.jpg, brother.jpg, sister.jpg
- grandfather.jpg, grandmother.jpg
- head.jpg, eye.jpg, ear.jpg, nose.jpg, mouth.jpg, hand.jpg, foot.jpg
- bird.jpg, lion.jpg, monkey.jpg
- banana.jpg, cake.jpg, water.jpg, milk.jpg, bread.jpg
- placeholder.jpg (صورة بديلة)

### الأصوات الاختيارية
يمكن إضافة ملفات صوتية في مجلد `sounds/`:
- correct.mp3 (صوت الإجابة الصحيحة)
- wrong.mp3 (صوت الإجابة الخاطئة)
- background.mp3 (موسيقى خلفية)

## 🤝 المساهمة

لإضافة مستوى جديد:
1. أنشئ ملف `levelX.js` في مجلد `js/`
2. أضف بيانات الألعاب والوظائف
3. حدث `levelData` في `main.js`
4. أضف الصور المطلوبة

## 📄 الترخيص

هذا المشروع مفتوح المصدر ومخصص للأغراض التعليمية.
