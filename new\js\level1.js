// بيانات المستوى الأول - الصف الأول الابتدائي

// بيانات لعبة الحروف الأبجدية
const alphabetData = [
    { letter: 'A', word: 'Apple', arabic: 'تفاحة', image: 'images/placeholder.svg', emoji: '🍎' },
    { letter: 'B', word: 'Ball', arabic: 'كرة', image: 'images/placeholder.svg', emoji: '⚽' },
    { letter: 'C', word: 'Cat', arabic: 'قطة', image: 'images/placeholder.svg', emoji: '🐱' },
    { letter: 'D', word: 'Dog', arabic: 'كلب', image: 'images/placeholder.svg', emoji: '🐶' },
    { letter: 'E', word: 'Elephant', arabic: 'فيل', image: 'images/placeholder.svg', emoji: '🐘' },
    { letter: 'F', word: 'Fish', arabic: 'سمكة', image: 'images/placeholder.svg', emoji: '🐟' },
    { letter: 'G', word: 'Giraffe', arabic: 'زرافة', image: 'images/placeholder.svg', emoji: '🦒' },
    { letter: 'H', word: 'House', arabic: 'منزل', image: 'images/placeholder.svg', emoji: '🏠' }
];

// بيانات لعبة الألوان
const colorsData = [
    { color: 'Red', arabic: 'أحمر', hex: '#FF0000', items: ['Apple', 'Rose', 'Fire truck'] },
    { color: 'Blue', arabic: 'أزرق', hex: '#0000FF', items: ['Sky', 'Ocean', 'Blueberry'] },
    { color: 'Yellow', arabic: 'أصفر', hex: '#FFFF00', items: ['Sun', 'Banana', 'Lemon'] },
    { color: 'Green', arabic: 'أخضر', hex: '#00FF00', items: ['Grass', 'Tree', 'Frog'] },
    { color: 'Orange', arabic: 'برتقالي', hex: '#FFA500', items: ['Orange', 'Carrot', 'Pumpkin'] },
    { color: 'Purple', arabic: 'بنفسجي', hex: '#800080', items: ['Grapes', 'Flower', 'Eggplant'] },
    { color: 'Black', arabic: 'أسود', hex: '#000000', items: ['Night', 'Ant', 'Coal'] },
    { color: 'White', arabic: 'أبيض', hex: '#FFFFFF', items: ['Snow', 'Cloud', 'Milk'] }
];

// بيانات لعبة الأرقام
const numbersData = [
    { number: 1, word: 'One', arabic: 'واحد', items: 1 },
    { number: 2, word: 'Two', arabic: 'اثنان', items: 2 },
    { number: 3, word: 'Three', arabic: 'ثلاثة', items: 3 },
    { number: 4, word: 'Four', arabic: 'أربعة', items: 4 },
    { number: 5, word: 'Five', arabic: 'خمسة', items: 5 },
    { number: 6, word: 'Six', arabic: 'ستة', items: 6 },
    { number: 7, word: 'Seven', arabic: 'سبعة', items: 7 },
    { number: 8, word: 'Eight', arabic: 'ثمانية', items: 8 },
    { number: 9, word: 'Nine', arabic: 'تسعة', items: 9 },
    { number: 10, word: 'Ten', arabic: 'عشرة', items: 10 }
];

// بيانات لعبة العائلة
const familyData = [
    { word: 'Father', arabic: 'أب', image: 'images/placeholder.svg', description: 'Dad', emoji: '👨' },
    { word: 'Mother', arabic: 'أم', image: 'images/placeholder.svg', description: 'Mom', emoji: '👩' },
    { word: 'Brother', arabic: 'أخ', image: 'images/placeholder.svg', description: 'Boy sibling', emoji: '👦' },
    { word: 'Sister', arabic: 'أخت', image: 'images/placeholder.svg', description: 'Girl sibling', emoji: '👧' },
    { word: 'Grandfather', arabic: 'جد', image: 'images/placeholder.svg', description: 'Grandpa', emoji: '👴' },
    { word: 'Grandmother', arabic: 'جدة', image: 'images/placeholder.svg', description: 'Grandma', emoji: '👵' }
];

// بيانات لعبة أجزاء الجسم
const bodyPartsData = [
    { word: 'Head', arabic: 'رأس', image: 'images/placeholder.svg', emoji: '🗣️' },
    { word: 'Eye', arabic: 'عين', image: 'images/placeholder.svg', emoji: '👁️' },
    { word: 'Ear', arabic: 'أذن', image: 'images/placeholder.svg', emoji: '👂' },
    { word: 'Nose', arabic: 'أنف', image: 'images/placeholder.svg', emoji: '👃' },
    { word: 'Mouth', arabic: 'فم', image: 'images/placeholder.svg', emoji: '👄' },
    { word: 'Hand', arabic: 'يد', image: 'images/placeholder.svg', emoji: '✋' },
    { word: 'Foot', arabic: 'قدم', image: 'images/placeholder.svg', emoji: '🦶' }
];

// بيانات لعبة الحيوانات
const animalsData = [
    { word: 'Cat', arabic: 'قطة', image: 'images/placeholder.svg', sound: 'Meow', emoji: '🐱' },
    { word: 'Dog', arabic: 'كلب', image: 'images/placeholder.svg', sound: 'Woof', emoji: '🐶' },
    { word: 'Bird', arabic: 'طائر', image: 'images/placeholder.svg', sound: 'Tweet', emoji: '🐦' },
    { word: 'Elephant', arabic: 'فيل', image: 'images/placeholder.svg', sound: 'Trumpet', emoji: '🐘' },
    { word: 'Monkey', arabic: 'قرد', image: 'images/placeholder.svg', sound: 'Ooh ooh', emoji: '🐵' },
    { word: 'Lion', arabic: 'أسد', image: 'images/placeholder.svg', sound: 'Roar', emoji: '🦁' }
];

// بيانات لعبة الطعام
const foodData = [
    { word: 'Apple', arabic: 'تفاحة', image: 'images/placeholder.svg', category: 'fruit', emoji: '🍎' },
    { word: 'Banana', arabic: 'موزة', image: 'images/placeholder.svg', category: 'fruit', emoji: '🍌' },
    { word: 'Cake', arabic: 'كعكة', image: 'images/placeholder.svg', category: 'dessert', emoji: '🍰' },
    { word: 'Water', arabic: 'ماء', image: 'images/placeholder.svg', category: 'drink', emoji: '💧' },
    { word: 'Milk', arabic: 'حليب', image: 'images/placeholder.svg', category: 'drink', emoji: '🥛' },
    { word: 'Bread', arabic: 'خبز', image: 'images/placeholder.svg', category: 'food', emoji: '🍞' }
];

// وظائف ألعاب المستوى الأول

function loadAlphabetGame() {
    let currentQuestionIndex = 0;
    const questions = shuffleArray(alphabetData).slice(0, 5);
    
    function showQuestion() {
        if (currentQuestionIndex >= questions.length) {
            nextGame();
            return;
        }
        
        const question = questions[currentQuestionIndex];
        const wrongAnswers = shuffleArray(alphabetData.filter(item => item.letter !== question.letter)).slice(0, 2);
        const allOptions = shuffleArray([question, ...wrongAnswers]);
        
        const gameContent = document.getElementById('game-content');
        gameContent.innerHTML = `
            <div class="alphabet-game">
                <h2>🔤 لعبة الحروف الأبجدية</h2>
                <div class="question-container">
                    <div class="emoji-display" style="font-size: 4rem; margin: 20px;">${question.emoji}</div>
                    <img src="${question.image}" alt="${question.word}" class="question-image"
                         onerror="this.style.display='none'">
                    <p class="question-text">ما هو الحرف الأول من كلمة "${question.arabic}"؟</p>
                    <p class="question-text-en">What is the first letter of "${question.word}"?</p>
                    <div class="speak-container">
                        <button class="speak-btn" onclick="speak('${question.arabic}', 'ar')">🔊 العربية</button>
                        <button class="speak-btn" onclick="speak('${question.word}', 'en')">🔊 English</button>
                    </div>
                </div>
                <div class="options-container">
                    ${allOptions.map(option => `
                        <button class="option-btn letter-option" onclick="checkAlphabetAnswer('${option.letter}', '${question.letter}')">
                            ${option.letter}
                        </button>
                    `).join('')}
                </div>
                <div class="progress-text">السؤال ${currentQuestionIndex + 1} من ${questions.length}</div>
            </div>
        `;
    }
    
    window.checkAlphabetAnswer = function(selected, correct) {
        const isCorrect = selected === correct;
        recordAnswer(isCorrect);
        
        // تلوين الأزرار
        document.querySelectorAll('.letter-option').forEach(btn => {
            btn.disabled = true;
            if (btn.textContent.trim() === correct) {
                btn.style.background = '#4CAF50';
                btn.style.color = 'white';
            } else if (btn.textContent.trim() === selected && !isCorrect) {
                btn.style.background = '#f44336';
                btn.style.color = 'white';
            }
        });
        
        if (isCorrect) {
            speak('أحسنت!', 'ar');
            playSound('correct');
        } else {
            speak('حاول مرة أخرى', 'ar');
            playSound('wrong');
        }
        
        setTimeout(() => {
            currentQuestionIndex++;
            showQuestion();
        }, 2000);
    };
    
    showQuestion();
}

function loadColorsGame() {
    let currentQuestionIndex = 0;
    const questions = shuffleArray(colorsData).slice(0, 6);
    
    function showQuestion() {
        if (currentQuestionIndex >= questions.length) {
            nextGame();
            return;
        }
        
        const question = questions[currentQuestionIndex];
        const wrongAnswers = shuffleArray(colorsData.filter(item => item.color !== question.color)).slice(0, 2);
        const allOptions = shuffleArray([question, ...wrongAnswers]);
        
        const gameContent = document.getElementById('game-content');
        gameContent.innerHTML = `
            <div class="colors-game">
                <h2>🎨 لعبة الألوان</h2>
                <div class="question-container">
                    <div class="color-display" style="background-color: ${question.hex}; width: 200px; height: 200px; border-radius: 20px; margin: 20px auto; border: 5px solid #333;"></div>
                    <p class="question-text">ما هو اسم هذا اللون؟</p>
                    <p class="question-text-en">What is the name of this color?</p>
                    <div class="speak-container">
                        <button class="speak-btn" onclick="speak('${question.arabic}', 'ar')">🔊 العربية</button>
                        <button class="speak-btn" onclick="speak('${question.color}', 'en')">🔊 English</button>
                    </div>
                </div>
                <div class="options-container">
                    ${allOptions.map(option => `
                        <button class="option-btn color-option" onclick="checkColorAnswer('${option.color}', '${question.color}')">
                            <div style="background-color: ${option.hex}; width: 30px; height: 30px; border-radius: 50%; display: inline-block; margin-left: 10px;"></div>
                            ${option.arabic} - ${option.color}
                        </button>
                    `).join('')}
                </div>
                <div class="progress-text">السؤال ${currentQuestionIndex + 1} من ${questions.length}</div>
            </div>
        `;
    }
    
    window.checkColorAnswer = function(selected, correct) {
        const isCorrect = selected === correct;
        recordAnswer(isCorrect);
        
        // تلوين الأزرار
        document.querySelectorAll('.color-option').forEach(btn => {
            btn.disabled = true;
            if (btn.textContent.includes(correct)) {
                btn.style.background = '#4CAF50';
                btn.style.color = 'white';
            } else if (btn.textContent.includes(selected) && !isCorrect) {
                btn.style.background = '#f44336';
                btn.style.color = 'white';
            }
        });
        
        if (isCorrect) {
            speak('ممتاز!', 'ar');
            playSound('correct');
        } else {
            speak('حاول مرة أخرى', 'ar');
            playSound('wrong');
        }
        
        setTimeout(() => {
            currentQuestionIndex++;
            showQuestion();
        }, 2000);
    };
    
    showQuestion();
}

function loadNumbersGame() {
    let currentQuestionIndex = 0;
    const questions = shuffleArray(numbersData).slice(0, 5);
    
    function showQuestion() {
        if (currentQuestionIndex >= questions.length) {
            nextGame();
            return;
        }
        
        const question = questions[currentQuestionIndex];
        const wrongAnswers = shuffleArray(numbersData.filter(item => item.number !== question.number)).slice(0, 2);
        const allOptions = shuffleArray([question, ...wrongAnswers]);
        
        // إنشاء عناصر بصرية للعدد
        const visualItems = Array(question.items).fill('⭐').join(' ');
        
        const gameContent = document.getElementById('game-content');
        gameContent.innerHTML = `
            <div class="numbers-game">
                <h2>🔢 لعبة الأرقام</h2>
                <div class="question-container">
                    <div class="visual-count" style="font-size: 2rem; margin: 20px; line-height: 1.5;">${visualItems}</div>
                    <p class="question-text">كم عدد النجوم؟</p>
                    <p class="question-text-en">How many stars?</p>
                    <div class="speak-container">
                        <button class="speak-btn" onclick="speak('${question.arabic}', 'ar')">🔊 العربية</button>
                        <button class="speak-btn" onclick="speak('${question.word}', 'en')">🔊 English</button>
                    </div>
                </div>
                <div class="options-container">
                    ${allOptions.map(option => `
                        <button class="option-btn number-option" onclick="checkNumberAnswer(${option.number}, ${question.number})">
                            ${option.number} - ${option.arabic} - ${option.word}
                        </button>
                    `).join('')}
                </div>
                <div class="progress-text">السؤال ${currentQuestionIndex + 1} من ${questions.length}</div>
            </div>
        `;
    }
    
    window.checkNumberAnswer = function(selected, correct) {
        const isCorrect = selected === correct;
        recordAnswer(isCorrect);
        
        // تلوين الأزرار
        document.querySelectorAll('.number-option').forEach(btn => {
            btn.disabled = true;
            if (btn.textContent.startsWith(correct.toString())) {
                btn.style.background = '#4CAF50';
                btn.style.color = 'white';
            } else if (btn.textContent.startsWith(selected.toString()) && !isCorrect) {
                btn.style.background = '#f44336';
                btn.style.color = 'white';
            }
        });
        
        if (isCorrect) {
            speak('رائع!', 'ar');
            playSound('correct');
        } else {
            speak('حاول مرة أخرى', 'ar');
            playSound('wrong');
        }
        
        setTimeout(() => {
            currentQuestionIndex++;
            showQuestion();
        }, 2000);
    };
    
    showQuestion();
}

// باقي الألعاب (العائلة، أجزاء الجسم، الحيوانات، الطعام) ستكون بنفس النمط
function loadFamilyGame() {
    loadGenericGame(familyData, 'العائلة', '👨‍👩‍👧‍👦', 'من هذا؟', 'Who is this?');
}

function loadBodyPartsGame() {
    loadGenericGame(bodyPartsData, 'أجزاء الجسم', '👤', 'ما هو هذا الجزء من الجسم؟', 'What is this body part?');
}

function loadAnimalsGame() {
    loadGenericGame(animalsData, 'الحيوانات', '🐾', 'ما هو هذا الحيوان؟', 'What is this animal?');
}

function loadFoodGame() {
    loadGenericGame(foodData, 'الطعام والشراب', '🍎', 'ما هذا؟', 'What is this?');
}

// وظيفة عامة للألعاب المتشابهة
function loadGenericGame(data, gameName, emoji, questionAr, questionEn) {
    let currentQuestionIndex = 0;
    const questions = shuffleArray(data).slice(0, 5);
    
    function showQuestion() {
        if (currentQuestionIndex >= questions.length) {
            nextGame();
            return;
        }
        
        const question = questions[currentQuestionIndex];
        const wrongAnswers = shuffleArray(data.filter(item => item.word !== question.word)).slice(0, 2);
        const allOptions = shuffleArray([question, ...wrongAnswers]);
        
        const gameContent = document.getElementById('game-content');
        gameContent.innerHTML = `
            <div class="generic-game">
                <h2>${emoji} لعبة ${gameName}</h2>
                <div class="question-container">
                    <div class="emoji-display" style="font-size: 4rem; margin: 20px;">${question.emoji || '❓'}</div>
                    <img src="${question.image}" alt="${question.word}" class="question-image"
                         onerror="this.style.display='none'">
                    <p class="question-text">${questionAr}</p>
                    <p class="question-text-en">${questionEn}</p>
                    <div class="speak-container">
                        <button class="speak-btn" onclick="speak('${question.arabic}', 'ar')">🔊 العربية</button>
                        <button class="speak-btn" onclick="speak('${question.word}', 'en')">🔊 English</button>
                    </div>
                </div>
                <div class="options-container">
                    ${allOptions.map(option => `
                        <button class="option-btn generic-option" onclick="checkGenericAnswer('${option.word}', '${question.word}')">
                            ${option.emoji || '❓'} ${option.arabic} - ${option.word}
                        </button>
                    `).join('')}
                </div>
                <div class="progress-text">السؤال ${currentQuestionIndex + 1} من ${questions.length}</div>
            </div>
        `;
    }
    
    window.checkGenericAnswer = function(selected, correct) {
        const isCorrect = selected === correct;
        recordAnswer(isCorrect);
        
        // تلوين الأزرار
        document.querySelectorAll('.generic-option').forEach(btn => {
            btn.disabled = true;
            if (btn.textContent.includes(correct)) {
                btn.style.background = '#4CAF50';
                btn.style.color = 'white';
            } else if (btn.textContent.includes(selected) && !isCorrect) {
                btn.style.background = '#f44336';
                btn.style.color = 'white';
            }
        });
        
        if (isCorrect) {
            speak('عظيم!', 'ar');
            playSound('correct');
        } else {
            speak('حاول مرة أخرى', 'ar');
            playSound('wrong');
        }
        
        setTimeout(() => {
            currentQuestionIndex++;
            showQuestion();
        }, 2000);
    };
    
    showQuestion();
}
