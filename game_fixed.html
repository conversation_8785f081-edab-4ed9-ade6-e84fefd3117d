<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🍍 جزيرة الأناناس - مغامرة اللغة</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Comic Sans MS', cursive, Arial, sans-serif;
            background: linear-gradient(45deg, #ff6b35, #f7931e, #ffd23f, #4CAF50);
            background-size: 400% 400%;
            animation: gradientShift 8s ease infinite;
            min-height: 100vh;
            overflow-x: hidden;
            position: relative;
        }

        @keyframes gradientShift {
            0% { background-position: 0% 50%; }
            50% { background-position: 100% 50%; }
            100% { background-position: 0% 50%; }
        }

        .container {
            max-width: 100%;
            margin: 0 auto;
            padding: 10px;
            position: relative;
            z-index: 1;
        }

        /* العناصر العائمة */
        .floating-element {
            position: absolute;
            animation: float 6s ease-in-out infinite;
            z-index: 0;
        }

        @keyframes float {
            0%, 100% { transform: translateY(0px) rotate(0deg); }
            50% { transform: translateY(-20px) rotate(5deg); }
        }

        /* الشاشات */
        .screen {
            background-color: rgba(255, 255, 255, 0.9);
            border-radius: 20px;
            box-shadow: 0 10px 20px rgba(0, 0, 0, 0.2);
            padding: 20px;
            margin: 20px auto;
            max-width: 800px;
            text-align: center;
        }

        /* زر البدء */
        .start-btn {
            background-color: #4CAF50;
            color: white;
            border: none;
            border-radius: 50px;
            padding: 15px 30px;
            font-size: 24px;
            cursor: pointer;
            margin: 20px 0;
            transition: all 0.3s ease;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
        }

        .start-btn:hover {
            background-color: #45a049;
            transform: scale(1.05);
        }

        /* عنوان اللعبة */
        h1 {
            color: #333;
            font-size: 36px;
            margin-bottom: 10px;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.1);
        }

        h2 {
            color: #444;
            font-size: 28px;
            margin-bottom: 20px;
        }

        /* صورة التحدي */
        .challenge-image {
            width: 100%;
            max-width: 400px;
            border-radius: 15px;
            margin: 15px auto;
            display: block;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
        }

        /* حاوية الخيارات */
        .options-container {
            display: flex;
            flex-wrap: wrap;
            justify-content: center;
            gap: 15px;
            margin-top: 20px;
        }

        /* أزرار الخيارات */
        .vocab-option, .grammar-option, .reading-option {
            background-color: #f8f8f8;
            border: 2px solid #ddd;
            border-radius: 10px;
            padding: 12px 20px;
            font-size: 18px;
            cursor: pointer;
            transition: all 0.2s ease;
            min-width: 200px;
        }

        .vocab-option:hover, .grammar-option:hover, .reading-option:hover {
            background-color: #e9e9e9;
            transform: translateY(-3px);
        }

        /* أنماط الإجابات الصحيحة والخاطئة */
        .correct {
            background-color: #4CAF50 !important;
            color: white;
            border-color: #45a049;
        }

        .wrong {
            background-color: #f44336 !important;
            color: white;
            border-color: #d32f2f;
        }

        /* شريط التقدم */
        .progress-container {
            width: 100%;
            background-color: #f1f1f1;
            border-radius: 10px;
            margin: 15px 0;
            overflow: hidden;
        }

        .progress-bar {
            height: 20px;
            background-color: #4CAF50;
            width: 0%;
            transition: width 0.5s ease;
        }

        /* مجموعة المفاتيح */
        .key-collection {
            display: flex;
            justify-content: center;
            gap: 15px;
            margin: 10px 0;
        }

        .key {
            font-size: 24px;
            opacity: 0.3;
            transition: all 0.3s ease;
        }

        .key.collected {
            opacity: 1;
            transform: scale(1.2);
        }

        /* أزرار النطق */
        .speak-container {
            display: flex;
            justify-content: center;
            gap: 10px;
            margin: 10px 0;
        }

        .speak-btn {
            background-color: #2196F3;
            color: white;
            border: none;
            border-radius: 20px;
            padding: 8px 15px;
            font-size: 16px;
            cursor: pointer;
            transition: all 0.2s ease;
        }

        .speak-btn:hover {
            background-color: #0b7dda;
        }

        .speak-btn.speaking {
            animation: pulse 1s infinite;
        }

        @keyframes pulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.05); }
            100% { transform: scale(1); }
        }

        /* نص القصة */
        .story-text {
            background-color: #f9f9f9;
            border-radius: 10px;
            padding: 15px;
            margin: 15px 0;
            text-align: right;
            line-height: 1.6;
        }

        /* حاوية السؤال */
        .question-container {
            background-color: #f0f8ff;
            border-radius: 10px;
            padding: 15px;
            margin: 15px 0;
            font-weight: bold;
        }

        /* شاشة النصر */
        .victory-screen {
            background: linear-gradient(135deg, #6dd5ed, #2193b0);
            color: white;
            text-align: center;
            padding: 30px;
            border-radius: 20px;
        }

        .victory-title {
            font-size: 36px;
            margin-bottom: 20px;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.2);
        }

        .evaluation-container {
            background-color: rgba(255, 255, 255, 0.9);
            border-radius: 15px;
            padding: 20px;
            margin: 20px 0;
            color: #333;
        }

        .score-breakdown {
            display: flex;
            flex-wrap: wrap;
            justify-content: space-around;
            margin: 20px 0;
        }

        .score-item {
            background-color: #f8f8f8;
            border-radius: 10px;
            padding: 10px 20px;
            margin: 10px;
            min-width: 150px;
        }

        .player-level {
            font-size: 24px;
            font-weight: bold;
            margin: 15px 0;
            color: #2193b0;
        }

        .play-again-btn {
            background-color: #ff6b35;
            color: white;
            border: none;
            border-radius: 30px;
            padding: 12px 25px;
            font-size: 20px;
            cursor: pointer;
            margin: 20px 0;
            transition: all 0.3s ease;
        }

        .play-again-btn:hover {
            background-color: #e55a2b;
            transform: scale(1.05);
        }
    </style>
</head>
<body>
    <div class="container">
        <div id="start-screen" class="screen">
            <h1>🍍 جزيرة الأناناس 🍍</h1>
            <h2>مغامرة تعلم اللغة - Language Learning Adventure</h2>
            <p>انضم إلى مغامرة ممتعة لتعلم اللغة! اجمع المفاتيح وافتح الكنز!</p>
            <p>Join a fun language learning adventure! Collect keys and unlock the treasure!</p>
            <button id="start-btn" class="start-btn" onclick="startGame()">ابدأ المغامرة - Start Adventure</button>
        </div>

        <script>
            // متغيرات اللعبة
            let vocabScore = 0;
            let currentVocabIndex = 0;
            let grammarScore = 0;
            let currentGrammarIndex = 0;
            
            // وظيفة بدء اللعبة
            function startGame() {
                // إخفاء شاشة البداية
                document.getElementById('start-screen').style.display = 'none';
                
                // إعادة تعيين متغيرات اللعبة
                vocabScore = 0;
                currentVocabIndex = 0;
                
                // إظهار شاشة المفردات
                showVocabScreen();
            }
            
            // وظيفة إظهار شاشة المفردات
            function showVocabScreen() {
                // التحقق من وجود شاشة المفردات
                let vocabScreen = document.getElementById('vocabulary');
                if (!vocabScreen) {
                    // إنشاء شاشة المفردات إذا لم تكن موجودة
                    vocabScreen = document.createElement('div');
                    vocabScreen.id = 'vocabulary';
                    vocabScreen.className = 'screen';
                    vocabScreen.innerHTML = `
                        <div class="container">
                            <h2>تحدي المفردات - Vocabulary Challenge</h2>
                            <div class="key-collection">
                                <div id="vocabKey1" class="key">🔑</div>
                                <div id="vocabKey2" class="key">🔑</div>
                                <div id="vocabKey3" class="key">🔑</div>
                            </div>
                            <div class="progress-container">
                                <div id="vocab-progress" class="progress-bar"></div>
                            </div>
                            <div id="vocab-score">النقاط - Score: 0/3</div>
                            <div id="vocab-challenge">
                                <img id="vocab-image" src="" alt="Challenge Image" class="challenge-image">
                                <p id="vocab-question"></p>
                                <div class="speak-container">
                                    <button class="speak-btn" onclick="speak(vocabData[currentVocabIndex].arabic, 'ar')">🔊 العربية</button>
                                    <button class="speak-btn" onclick="speak(vocabData[currentVocabIndex].english, 'en')">🔊 English</button>
                                </div>
                                <div class="options-container">
                                    <button class="vocab-option"></button>
                                    <button class="vocab-option"></button>
                                    <button class="vocab-option"></button>
                                </div>
                            </div>
                        </div>
                    `;
                    document.body.appendChild(vocabScreen);
                }
                
                // إظهار شاشة المفردات
                vocabScreen.style.display = 'block';
                
                // تحميل أول تحدي للمفردات
                loadVocabChallenge();
            }
            
            // بيانات تحديات المفردات
            const vocabData = [
                {
                    image: 'images/apple.jpg',
                    question: 'ما هذه الفاكهة؟ - What is this fruit?',
                    arabic: 'تفاحة',
                    english: 'Apple',
                    correct: 'تفاحة - Apple',
                    options: [
                        'تفاحة - Apple',
                        'موزة - Banana',
                        'برتقالة - Orange'
                    ]
                },
                {
                    image: 'images/crocodile.jpg',
                    question: 'ما هذا الحيوان؟ - What is this animal?',
                    arabic: 'تمساح',
                    english: 'Crocodile',
                    correct: 'تمساح - Crocodile',
                    options: [
                        'أسد - Lion',
                        'تمساح - Crocodile',
                        'فيل - Elephant'
                    ]
                },
                {
                    image: 'images/car.jpg',
                    question: 'ما هذه وسيلة النقل؟ - What is this transportation?',
                    arabic: 'سيارة',
                    english: 'Car',
                    correct: 'سيارة - Car',
                    options: [
                        'طائرة - Airplane',
                        'قطار - Train',
                        'سيارة - Car'
                    ]
                }
            ];
            
            // وظيفة خلط المصفوفة
            function shuffleArray(array) {
                for (let i = array.length - 1; i > 0; i--) {
                    const j = Math.floor(Math.random() * (i + 1));
                    [array[i], array[j]] = [array[j], array[i]];
                }
                return array;
            }
            
            // وظيفة تحميل تحدي المفردات
            function loadVocabChallenge() {
                console.log('تحميل تحدي المفردات رقم:', currentVocabIndex + 1);
                
                if (currentVocabIndex >= vocabData.length) {
                    // انتهاء التحدي والانتقال إلى مرحلة القواعد
                    alert('أحسنت! لقد أكملت تحدي المفردات - Well done! You completed the vocabulary challenge');
                    showGrammarScreen();
                    return;
                }
                
                const data = vocabData[currentVocabIndex];
                document.getElementById('vocab-image').src = data.image;
                document.getElementById('vocab-question').textContent = data.question;
                
                // تحديث شريط التقدم
                const progressBar = document.getElementById('vocab-progress');
                progressBar.style.width = `${(currentVocabIndex / vocabData.length) * 100}%`;
                
                // تحديث النقاط
                document.getElementById('vocab-score').textContent = `النقاط - Score: ${vocabScore}/${vocabData.length}`;
                
                // خلط الخيارات
                const shuffledOptions = [...data.options];
                shuffleArray(shuffledOptions);
                
                console.log('الكلمة الصحيحة:', data.correct);
                console.log('الخيارات المتاحة:', shuffledOptions);
                
                // عرض الخيارات
                const options = resetOptionButtons('.vocab-option');
                for (let i = 0; i < options.length; i++) {
                    const option = options[i];
                    option.textContent = shuffledOptions[i];
                    option.onclick = function() {
                        console.log(`تم النقر على: ${this.textContent}, الإجابة الصحيحة: ${data.correct}`);
                        checkVocab(this, data.correct);
                    };
                }
            }
            
            // وظيفة التحقق من إجابة المفردات
            function checkVocab(button, correctAnswer) {
                console.log('checkVocab called with correctAnswer:', correctAnswer);
                const isCorrect = button.textContent === correctAnswer;
                
                const options = document.querySelectorAll('.vocab-option');
                options.forEach(opt => {
                    opt.disabled = true; // تعطيل الأزرار مؤقتًا
                    if (opt.textContent === correctAnswer) {
                        opt.classList.add('correct');
                    } else {
                        opt.classList.add('wrong');
                    }
                });
                
                if (isCorrect) {
                    vocabScore++;
                    // جمع المفتاح
                    document.getElementById(`vocabKey${vocabScore}`).classList.add('collected');
                    // تحديث النتيجة
                    document.getElementById('vocab-score').textContent = `النقاط - Score: ${vocabScore}/${vocabData.length}`;
                }
                
                // الانتقال إلى السؤال التالي بعد تأخير
                setTimeout(() => {
                    currentVocabIndex++;
                    loadVocabChallenge();
                }, 1500);
            }
            
            // وظيفة إظهار شاشة القواعد
            function showGrammarScreen() {
                // إخفاء شاشة المفردات
                document.getElementById('vocabulary').style.display = 'none';
                
                // التحقق من وجود شاشة القواعد
                let grammarScreen = document.getElementById('grammar');
                if (!grammarScreen) {
                    // إنشاء شاشة القواعد إذا لم تكن موجودة
                    grammarScreen = document.createElement('div');
                    grammarScreen.id = 'grammar';
                    grammarScreen.className = 'screen';
                    grammarScreen.innerHTML = `
                        <div class="container">
                            <h2>تحدي القواعد - Grammar Challenge</h2>
                            <div class="key-collection">
                                <div id="grammarKey1" class="key">🔑</div>
                                <div id="grammarKey2" class="key">🔑</div>
                                <div id="grammarKey3" class="key">🔑</div>
                            </div>
                            <div class="progress-container">
                                <div id="grammar-progress" class="progress-bar"></div>
                            </div>
                            <div id="grammar-score">النقاط - Score: 0/3</div>
                            <div id="grammar-challenge">
                                <img id="grammar-image" src="" alt="Challenge Image" class="challenge-image">
                                <p id="grammar-question"></p>
                                <div class="speak-container">
                                    <button class="speak-btn" onclick="speak(grammarData[currentGrammarIndex].arabic, 'ar')">🔊 العربية</button>
                                    <button class="speak-btn" onclick="speak(grammarData[currentGrammarIndex].english, 'en')">🔊 English</button>
                                </div>
                                <div class="options-container">
                                    <button class="grammar-option"></button>
                                    <button class="grammar-option"></button>
                                    <button class="grammar-option"></button>
                                </div>
                            </div>
                        </div>
                    `;
                    document.body.appendChild(grammarScreen);
                }
                
                // إظهار شاشة القواعد
                grammarScreen.style.display = 'block';
                
                // تهيئة متغيرات اللعبة للقواعد
                grammarScore = 0;
                currentGrammarIndex = 0;
                
                // تحميل أول تحدي للقواعد
                loadGrammarChallenge();
            }
            
            // متغيرات لعبة القواعد
            let grammarScore = 0;
            let currentGrammarIndex = 0;
            
            // بيانات تحديات القواعد
            const grammarData = [
                {
                    image: 'images/grammar1.jpg',
                    question: 'اختر الجملة الصحيحة - Choose the correct sentence',
                    arabic: 'أي جملة صحيحة نحوياً؟',
                    english: 'Which sentence is grammatically correct?',
                    correct: 'The cat is sleeping on the bed.',
                    options: [
                        'The cat sleeping on the bed.',
                        'The cat is sleeping on the bed.',
                        'The cat sleep on the bed.'
                    ]
                },
                {
                    image: 'images/grammar2.jpg',
                    question: 'اختر الجملة الصحيحة - Choose the correct sentence',
                    arabic: 'أي جملة صحيحة نحوياً؟',
                    english: 'Which sentence is grammatically correct?',
                    correct: 'She is reading a book.',
                    options: [
                        'She reading a book.',
                        'She is reading a book.',
                        'She read a book now.'
                    ]
                },
                {
                    image: 'images/grammar3.jpg',
                    question: 'اختر الجملة الصحيحة - Choose the correct sentence',
                    arabic: 'أي جملة صحيحة نحوياً؟',
                    english: 'Which sentence is grammatically correct?',
                    correct: 'They went to the park yesterday.',
                    options: [
                        'They go to the park yesterday.',
                        'They went to the park yesterday.',
                        'They going to the park yesterday.'
                    ]
                }
            ];
            
            // وظيفة تحميل تحدي القواعد
            function loadGrammarChallenge() {
                if (currentGrammarIndex >= grammarData.length) {
                    // انتهاء التحدي والانتقال إلى مرحلة القراءة
                    alert('أحسنت! لقد أكملت تحدي القواعد - Well done! You completed the grammar challenge');
                    showReadingScreen();
                    return;
                }
                
                // تحميل بيانات التحدي الحالي
                const data = grammarData[currentGrammarIndex];
                
                // عرض الصورة والسؤال
                document.getElementById('grammar-image').src = data.image;
                document.getElementById('grammar-question').textContent = data.question;
                
                // تحديث شريط التقدم
                const progressBar = document.getElementById('grammar-progress');
                progressBar.style.width = `${(currentGrammarIndex + 1) / grammarData.length * 100}%`;
                
                // تحديث النقاط
                document.getElementById('grammar-score').textContent = `النقاط - Score: ${grammarScore}/${grammarData.length}`;
                
                // خلط الخيارات
                const shuffledOptions = [...data.options];
                shuffleArray(shuffledOptions);
                
                // عرض الخيارات
                const options = resetOptionButtons('.grammar-option');
                for (let i = 0; i < options.length; i++) {
                    const option = options[i];
                    option.textContent = shuffledOptions[i];
                    option.onclick = function() {
                        console.log(`تم النقر على: ${this.textContent}, الإجابة الصحيحة: ${data.correct}`);
                        checkGrammar(this, data.correct);
                    };
                }
                
                // تحديث أزرار النطق
                const speakButtons = document.querySelectorAll('#grammar .speak-btn');
                speakButtons[0].onclick = function() { speak(data.arabic, 'ar'); };
                speakButtons[1].onclick = function() { speak(data.english, 'en'); };
                
                console.log(`تم تحميل تحدي القواعد رقم ${currentGrammarIndex+1}، الكلمة الصحيحة: ${data.correct}`);
                console.log('الخيارات المتاحة:', shuffledOptions);
            }
            
            // وظيفة إظهار شاشة القراءة
            function showReadingScreen() {
                // إخفاء شاشة القواعد
                document.getElementById('grammar').style.display = 'none';
                
                // التحقق من وجود شاشة القراءة
                let readingScreen = document.getElementById('reading');
                if (!readingScreen) {
                    // إنشاء شاشة القراءة إذا لم تكن موجودة
                    readingScreen = document.createElement('div');
                    readingScreen.id = 'reading';
                    readingScreen.className = 'screen';
                    readingScreen.innerHTML = `
                        <div class="container">
                            <h2>تحدي القراءة - Reading Challenge</h2>
                            <div class="key-collection">
                                <div id="readingKey1" class="key">🔑</div>
                                <div id="readingKey2" class="key">🔑</div>
                            </div>
                            <div class="progress-container">
                                <div id="reading-progress" class="progress-bar"></div>
                            </div>
                            <div id="reading-score">النقاط - Score: 0/2</div>
                            <div id="reading-challenge">
                                <div class="story-text">
                                    <p id="reading-text"></p>
                                    <div class="speak-container">
                                        <button class="speak-btn" onclick="speak(readingData[currentReadingIndex].arabic, 'ar')">🔊 العربية</button>
                                        <button class="speak-btn" onclick="speak(readingData[currentReadingIndex].english, 'en')">🔊 English</button>
                                    </div>
                                </div>
                                <div class="question-container">
                                    <p id="reading-question"></p>
                                </div>
                                <div class="options-container">
                                    <button class="reading-option"></button>
                                    <button class="reading-option"></button>
                                    <button class="reading-option"></button>
                                </div>
                            </div>
                        </div>
                    `;
                    document.body.appendChild(readingScreen);
                }
                
                // إظهار شاشة القراءة
                readingScreen.style.display = 'block';
                
                // تهيئة متغيرات اللعبة للقراءة
                readingScore = 0;
                currentReadingIndex = 0;
                
                // تحميل أول تحدي للقراءة
                loadReadingChallenge();
            }
            
            // متغيرات لعبة القراءة
            let readingScore = 0;
            let currentReadingIndex = 0;
            
            // بيانات تحديات القراءة
            const readingData = [
                {
                    arabic: "ذهب أحمد إلى الشاطئ. كان الجو مشمساً والبحر هادئاً. بنى أحمد قلعة من الرمل وسبح في الماء.",
                    english: "Ahmed went to the beach. The weather was sunny and the sea was calm. Ahmed built a sand castle and swam in the water.",
                    question: "ماذا بنى أحمد على الشاطئ؟ - What did Ahmed build on the beach?",
                    correct: "قلعة من الرمل - A sand castle",
                    options: [
                        "قلعة من الرمل - A sand castle",
                        "منزلاً من الحجارة - A stone house",
                        "طائرة ورقية - A kite"
                    ]
                },
                {
                    arabic: "ذهبت سارة إلى الحديقة. رأت فراشات ملونة وأزهاراً جميلة. جلست تحت شجرة وقرأت كتاباً.",
                    english: "Sara went to the garden. She saw colorful butterflies and beautiful flowers. She sat under a tree and read a book.",
                    question: "ماذا فعلت سارة تحت الشجرة؟ - What did Sara do under the tree?",
                    correct: "قرأت كتاباً - Read a book",
                    options: [
                        "نامت - Slept",
                        "قرأت كتاباً - Read a book",
                        "أكلت طعاماً - Ate food"
                    ]
                }
            ];
            
            // وظيفة تحميل تحدي القراءة
            function loadReadingChallenge() {
                if (currentReadingIndex >= readingData.length) {
                    // انتهاء التحدي والانتقال إلى شاشة النصر
                    alert('أحسنت! لقد أكملت تحدي القراءة - Well done! You completed the reading challenge');
                    showVictoryScreen();
                    return;
                }
                
                const data = readingData[currentReadingIndex];
                document.getElementById('reading-text').textContent = data.arabic + "\n" + data.english;
                document.getElementById('reading-question').textContent = data.question;
                
                // خلط الخيارات
                const shuffledOptions = [...data.options].sort(() => Math.random() - 0.5);
                
                // إعادة تعيين الأزرار
                const options = resetOptionButtons('.reading-option');
                for (let i = 0; i < options.length && i < shuffledOptions.length; i++) {
                    const option = options[i];
                    option.textContent = shuffledOptions[i];
                    option.onclick = function() {
                        const isCorrect = this.textContent === data.correct;
                        console.log('تم النقر على:', this.textContent, 'هل هو صحيح؟', isCorrect);
                        checkReading(this, isCorrect);
                    };
                }
                
                // تحديث شريط التقدم
                document.getElementById('reading-progress').style.width = `${(currentReadingIndex / readingData.length) * 100}%`;
                document.getElementById('reading-score').textContent = `النقاط - Score: ${readingScore}/${readingData.length}`;
            }
            
            // وظيفة التحقق من إجابة القراءة
            function checkReading(button, isCorrect) {
                console.log('checkReading called with isCorrect:', isCorrect);
                const options = document.querySelectorAll('.reading-option');
                options.forEach(opt => {
                    opt.disabled = true; // تعطيل الأزرار مؤقتًا
                    if (opt.textContent === readingData[currentReadingIndex].correct) {
                        opt.classList.add('correct');
                    } else {
                        opt.classList.add('wrong');
                    }
                });
                
                if (isCorrect) {
                    readingScore++;
                    // جمع المفتاح
                    document.getElementById(`readingKey${readingScore}`).classList.add('collected');
                    // تحديث النتيجة
                    document.getElementById('reading-score').textContent = `النقاط - Score: ${readingScore}/${readingData.length}`;
                }
                
                // الانتقال إلى السؤال التالي بعد تأخير
                setTimeout(() => {
                    currentReadingIndex++;
                    loadReadingChallenge(); // سيقوم بإعادة تفعيل الأزرار
                }, 1500);
            }
            
            // وظيفة إظهار شاشة النصر والتقييم النهائي
            function showVictoryScreen() {
                // إخفاء شاشة القراءة
                document.getElementById('reading').style.display = 'none';
                
                // حساب النتيجة الإجمالية
                const totalScore = vocabScore + grammarScore + readingScore;
                const maxScore = vocabData.length + grammarData.length + readingData.length;
                const scorePercentage = Math.round((totalScore / maxScore) * 100);
                
                // تحديد مستوى اللاعب بناءً على النسبة المئوية
                let playerLevel, playerLevelArabic, playerLevelEmoji;
                if (scorePercentage >= 90) {
                    playerLevel = "Expert";
                    playerLevelArabic = "خبير";
                    playerLevelEmoji = "🌟🌟🌟";
                } else if (scorePercentage >= 70) {
                    playerLevel = "Advanced";
                    playerLevelArabic = "متقدم";
                    playerLevelEmoji = "🌟🌟";
                } else if (scorePercentage >= 50) {
                    playerLevel = "Intermediate";
                    playerLevelArabic = "متوسط";
                    playerLevelEmoji = "🌟";
                } else {
                    playerLevel = "Beginner";
                    playerLevelArabic = "مبتدئ";
                    playerLevelEmoji = "⭐";
                }
                
                // إنشاء شاشة النصر
                const victoryScreen = document.createElement('div');
                victoryScreen.id = 'victory';
                victoryScreen.className = 'screen';
                victoryScreen.innerHTML = `
                    <div class="victory-screen">
                        <h2 class="victory-title">🎉 مبروك! لقد أكملت المغامرة! 🎉</h2>
                        <h3>Congratulations! You've completed the adventure!</h3>
                        
                        <div class="evaluation-container">
                            <h3>تقييم اللاعب - Player Evaluation</h3>
                            
                            <div class="score-breakdown">
                                <div class="score-item">
                                    <h4>المفردات - Vocabulary</h4>
                                    <p>${vocabScore}/${vocabData.length}</p>
                                </div>
                                <div class="score-item">
                                    <h4>القواعد - Grammar</h4>
                                    <p>${grammarScore}/${grammarData.length}</p>
                                </div>
                                <div class="score-item">
                                    <h4>القراءة - Reading</h4>
                                    <p>${readingScore}/${readingData.length}</p>
                                </div>
                            </div>
                            
                            <div class="score-item">
                                <h4>النتيجة الإجمالية - Total Score</h4>
                                <p>${totalScore}/${maxScore} (${scorePercentage}%)</p>
                            </div>
                            
                            <div class="player-level">
                                <p>مستواك: ${playerLevelArabic} - ${playerLevel} ${playerLevelEmoji}</p>
                            </div>
                            
                            <p class="feedback">
                                ${scorePercentage >= 70 ? 
                                    'أحسنت! أنت تتقن اللغة بشكل جيد. استمر في التعلم!' : 
                                    'جيد! استمر في ممارسة اللغة لتحسين مستواك.'}
                            </p>
                            <p class="feedback">
                                ${scorePercentage >= 70 ? 
                                    'Well done! You have a good command of the language. Keep learning!' : 
                                    'Good job! Keep practicing to improve your language skills.'}
                            </p>
                        </div>
                        
                        <button class="play-again-btn" onclick="startGame()">العب مرة أخرى - Play Again</button>
                    </div>
                `;
                
                document.body.appendChild(victoryScreen);
            }
            
            // وظيفة مساعدة لإعادة تعيين أزرار الخيارات
            function resetOptionButtons(optionsSelector) {
                const options = document.querySelectorAll(optionsSelector);
                options.forEach(opt => {
                    opt.disabled = false; // إعادة تفعيل الأزرار
                    opt.classList.remove('correct', 'wrong'); // إزالة التنسيقات السابقة
                    opt.onclick = null; // إزالة معالج الحدث السابق
                });
                return options;
            }
            
            // وظيفة التحقق من إجابة القواعد
            function checkGrammar(button, correctAnswer) {
                console.log('checkGrammar called with correctAnswer:', correctAnswer);
                const isCorrect = button.textContent === correctAnswer;
                const options = document.querySelectorAll('.grammar-option');
                options.forEach(opt => {
                    opt.disabled = true; // تعطيل الأزرار مؤقتًا
                    if (opt.textContent === correctAnswer) {
                        opt.classList.add('correct');
                    } else {
                        opt.classList.add('wrong');
                    }
                });
                
                if (isCorrect) {
                    grammarScore++;
                    // جمع المفتاح
                    document.getElementById(`grammarKey${grammarScore}`).classList.add('collected');
                    // تحديث النتيجة
                    document.getElementById('grammar-score').textContent = `النقاط - Score: ${grammarScore}/${grammarData.length}`;
                }
                
                // الانتقال إلى السؤال التالي بعد تأخير
                setTimeout(() => {
                    currentGrammarIndex++;
                    loadGrammarChallenge(); // سيقوم بإعادة تفعيل الأزرار
                }, 1500);
            }
            
            // وظيفة النطق
            function speak(text, lang) {
                if ('speechSynthesis' in window) {
                    // إيقاف أي نطق حالي
                    speechSynthesis.cancel();

                    const utterance = new SpeechSynthesisUtterance(text);
                    utterance.lang = lang === 'ar' ? 'ar-SA' : 'en-US';
                    utterance.rate = 0.5; // نطق أبطأ للأطفال
                    utterance.pitch = 1.1; // نبرة أعلى قليلاً للأطفال
                    
                    // تأثير بصري للنطق
                    const speakButtons = document.querySelectorAll('.speak-btn');
                    speakButtons.forEach(btn => {
                        if (btn.textContent.includes(lang === 'ar' ? 'العربية' : 'English')) {
                            btn.classList.add('speaking');
                        }
                    });
                    
                    utterance.onend = function() {
                        speakButtons.forEach(btn => {
                            btn.classList.remove('speaking');
                        });
                    };
                    
                    // معالجة الأخطاء
                    utterance.onerror = function() {
                        speakButtons.forEach(btn => {
                            btn.classList.remove('speaking');
                        });
                        setTimeout(() => {
                            alert('حدث خطأ أثناء النطق - Error during speech synthesis');
                        }, 100);
                    };
                    
                    speechSynthesis.speak(utterance);
                } else {
                    alert('متصفحك لا يدعم خاصية النطق - Your browser does not support speech synthesis');
                }
            }
        </script>
    </div>
</body>
</html>